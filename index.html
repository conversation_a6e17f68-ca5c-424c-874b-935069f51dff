     <!-- step 2
              <div class="measuring-cylinder"></div>
              <div class="beaker"></div>
                 step 3 -->
              <!-- <div class="standard-solution"></div>
              <div class="test-tube-1"></div>
              <div class="test-tube-2"></div>
              <div class="test-tube-3"></div>
              <div class="test-tube-4"></div>
              <div class="test-tube-5"></div>
              <div class="test-tube-rack"></div> --> 


              <!-- <input type="button" id="measuring-cylinder" value="Measuring Cylinder">
                <input type="button" id="beaker" value="Beaker">
                step 3 -->
                 <!-- <input type="range" id="test-tube" value="Test Tube">
                 <button>Add test tube</button> -->
                 <!-- <label for="tubeRange">Test Tubes: <span id="tubeCount">0</span></label>
                 <input type="range" id="tubeRange" name="tubeRange" min="0" max="20" value="0">

                 <div id="tubeContainer"></div> --> 