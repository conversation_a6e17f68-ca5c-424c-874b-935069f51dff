document.addEventListener('DOMContentLoaded', function() {
    // Initially hide the main content until language is selected
    const container = document.querySelector('.container');
    container.style.display = 'none';
    container.style.visibility = 'hidden';

    // Language translations
    const translations = {
        'en': {
            'select_language': 'Select Your Language',
            'instruction': 'Instruction:- Dissolve 100 mg of salicylic acid in 100 ml of distilled water.',
            'apparatus_menu': 'Apparatus Menu',
            'salicylic_acid': 'Salicylic Acid',
            'distilled_water': 'Distilled Water',
            'volumetric_flasks': 'Volumetric Flasks',
            'start': 'Start',
            'next': 'Next'
        },
        'hi': {
            'select_language': 'अपनी भाषा चुनें',
            'instruction': 'निर्देश:- 100 मिलीग्राम सैलिसिलिक एसिड को 100 मिली आसुत जल में घोलें।',
            'apparatus_menu': 'उपकरण मेनू',
            'salicylic_acid': 'सैलिसिलिक एसिड',
            'distilled_water': 'आसुत जल',
            'volumetric_flasks': 'आयतनमापी फ्लास्क',
            'start': 'प्रारंभ करें',
            'next': 'अगला'
        },
        'bn': {
            'select_language': 'আপনার ভাষা নির্বাচন করুন',
            'instruction': 'নির্দেশ:- 100 মিলিগ্রাম স্যালিসিলিক অ্যাসিড 100 মিলি পাতিত জলে দ্রবীভূত করুন।',
            'apparatus_menu': 'যন্ত্রপাতি মেনু',
            'salicylic_acid': 'স্যালিসিলিক অ্যাসিড',
            'distilled_water': 'পাতিত জল',
            'volumetric_flasks': 'আয়তনমাপক ফ্লাস্ক',
            'start': 'শুরু করুন',
            'next': 'পরবর্তী'
        },
        'gu': {
            'select_language': 'તમારી ભાષા પસંદ કરો',
            'instruction': 'સૂચના:- 100 મિલિગ્રામ સેલિસિલિક એસિડને 100 મિલિ ડિસ્ટિલ્ડ વોટરમાં ઓગાળો।',
            'apparatus_menu': 'ઉપકરણ મેનુ',
            'salicylic_acid': 'સેલિસિલિક એસિડ',
            'distilled_water': 'ડિસ્ટિલ્ડ વોટર',
            'volumetric_flasks': 'વોલ્યુમેટ્રિક ફ્લાસ્ક',
            'start': 'શરૂ કરો',
            'next': 'આગળ'
        },
        'ml': {
            'select_language': 'നിങ്ങളുടെ ഭാഷ തിരഞ്ഞെടുക്കുക',
            'instruction': 'നിർദ്ദേശം:- 100 മില്ലിഗ്രാം സാലിസിലിക് ആസിഡ് 100 മില്ലി ഡിസ്റ്റിൽഡ് വാട്ടറിൽ ലയിപ്പിക്കുക।',
            'apparatus_menu': 'ഉപകരണ മെനു',
            'salicylic_acid': 'സാലിസിലിക് ആസിഡ്',
            'distilled_water': 'ഡിസ്റ്റിൽഡ് വാട്ടർ',
            'volumetric_flasks': 'വോള്യുമെട്രിക് ഫ്ലാസ്ക്',
            'start': 'ആരംഭിക്കുക',
            'next': 'അടുത്തത്'
        }
    };

    // Set default language (will be chosen by user)
    let currentLanguage = '';

    // Set initial popup title in English
    document.querySelector('.language-popup h2').textContent = translations['en'].select_language;

    // Handle language selection from popup
    const languageButtons = document.querySelectorAll('.lang-btn');
    languageButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Set the selected language
            currentLanguage = this.getAttribute('data-lang');

            // Update all text with the selected language
            updateLanguage();

            // Hide the language popup
            document.getElementById('language-popup').classList.add('hidden');

            // Show the main content with animation
            const container = document.querySelector('.container');
            container.style.visibility = 'visible';
            container.style.display = 'block';
            container.classList.add('animate-in');

            // Update the language dropdown to match the selected language
            document.querySelector('.language-select select').value = currentLanguage;
        });

        // Change popup title on hover to show it in that language
        button.addEventListener('mouseover', function() {
            const lang = this.getAttribute('data-lang');
            document.querySelector('.language-popup h2').textContent = translations[lang].select_language;
        });

        // Reset to English when not hovering
        button.addEventListener('mouseout', function() {
            document.querySelector('.language-popup h2').textContent = translations['en'].select_language;
        });
    });

    // Initially hide all apparatus elements
    document.querySelector('.volumetric-flasks').style.display = 'none';
    document.querySelector('.volumetric-flasks-cap').style.display = 'none';
    document.querySelector('.salicylic-acid').style.display = 'none';
    document.querySelector('.salicylic-acid-cap').style.display = 'none';
    document.querySelector('.distilled-water').style.display = 'none';

    // Initialize water and drop elements
    const waterElement = document.querySelector('.water-1');
    const drops = document.querySelectorAll('.drop');

    // Hide water element initially
    if (waterElement) {
        waterElement.style.display = 'none';
        waterElement.style.opacity = '0';
    }

    // Hide drops initially
    drops.forEach(drop => {
        drop.style.display = 'none';
        drop.style.opacity = '0';
    });

    // Hide the next button initially
    const nextButton = document.querySelector('.next-btn');
    if (nextButton) nextButton.style.display = 'none';

    // Add event listeners to buttons
    document.getElementById('volumetric-flasks').addEventListener('click', function() {
        toggleElement('.volumetric-flasks');
        toggleElement('.volumetric-flasks-cap');
    });

    // Flask interaction has been removed

    document.getElementById('salicylic-acid').addEventListener('click', function() {
        toggleElement('.salicylic-acid');
        toggleElement('.salicylic-acid-cap');
    });

    // Event handler for the distilled water menu button
    document.getElementById('distilled-water').addEventListener('click', function() {
        const distilledWaterElement = document.querySelector('.distilled-water');
        const waterElement = document.querySelector('.water-1');

        // Toggle the distilled water bottle
        if (distilledWaterElement.style.display === 'none') {
            distilledWaterElement.style.display = 'block';
            distilledWaterElement.classList.add('animate-in');
        } else {
            distilledWaterElement.classList.remove('animate-in');
            setTimeout(() => {
                distilledWaterElement.style.display = 'none';
            }, 300);

            // If we're hiding the bottle, also hide the water in the flask
            waterElement.style.opacity = '0';
            setTimeout(() => {
                waterElement.style.display = 'none';
            }, 1000);
        }
    });

    // Add click event to the distilled water bottle itself for the animation
    document.querySelector('.distilled-water').addEventListener('click', function() {
        // Only proceed if the flask is visible
        const flaskElement = document.querySelector('.volumetric-flasks');
        if (flaskElement.style.display === 'block') {
            // Get the water element and drops
            const waterElement = document.querySelector('.water-1');
            const drops = document.querySelectorAll('.drop');

            // Start the animation sequence
            // 1. Show and animate the drops falling
            drops.forEach((drop, index) => {
                setTimeout(() => {
                    // Make sure the drop is visible
                    drop.style.display = 'block';
                    drop.style.opacity = '1';

                    // Add the falling animation class
                    drop.classList.add('falling');
                    console.log('Drop animation started', index);

                    // Remove the animation class after it completes
                    setTimeout(() => {
                        drop.classList.remove('falling');
                        drop.style.opacity = '0';
                        drop.style.display = 'none';
                        console.log('Drop animation ended', index);
                    }, 1500); // Match the animation duration
                }, index * 300); // Stagger the drops
            });

            // 2. After drops have finished falling, show the water in the flask
            // Wait for the last drop to complete its animation (index * 300 + 1500)
            const lastDropDelay = (drops.length - 1) * 300 + 1000; // Wait until drops are almost done

            setTimeout(() => {
                // Show the water in the flask with a fade-in effect
                waterElement.style.display = 'block';
                setTimeout(() => {
                    waterElement.style.opacity = '1';

                    // 3. Show a message that water has been added
                    const instructionText = document.querySelector('.instruction-text');
                    const originalText = instructionText.textContent;

                    // Create water added message in the current language
                    const waterAddedMessages = {
                        'en': 'Distilled water has been added to the flask.',
                        'hi': '\u0906\u0938\u0941\u0924 \u091c\u0932 \u092b\u094d\u0932\u093e\u0938\u094d\u0915 \u092e\u0947\u0902 \u0921\u093e\u0932\u093e \u0917\u092f\u093e \u0939\u0948\u0964',
                        'bn': '\u09aa\u09be\u09a4\u09bf\u09a4 \u099c\u09b2 \u09ab\u09cd\u09b2\u09be\u09b8\u09cd\u0995\u09c7 \u09af\u09cb\u0997 \u0995\u09b0\u09be \u09b9\u09af\u09bc\u09c7\u099b\u09c7\u0964',
                        'gu': '\u0aa1\u0abf\u0ab8\u0acd\u0a9f\u0abf\u0ab2\u0acd\u0aa1 \u0ab5\u0acb\u0a9f\u0ab0 \u0aab\u0acd\u0ab2\u0abe\u0ab8\u0acd\u0a95\u0aae\u0abe\u0a82 \u0a89\u0aae\u0ac7\u0ab0\u0ab5\u0abe\u0aae\u0abe\u0a82 \u0a86\u0ab5\u0acd\u0aaf\u0ac1\u0a82 \u0a9b\u0ac7.',
                        'ml': '\u0d21\u0d3f\u0d38\u0d4d\u0d31\u0d4d\u0d31\u0d3f\u0d7d\u0d21\u0d4d \u0d35\u0d3e\u0d1f\u0d4d\u0d1f\u0d7c \u0d2b\u0d4d\u0d32\u0d3e\u0d38\u0d4d\u0d15\u0d3f\u0d32\u0d47\u0d15\u0d4d\u0d15\u0d4d \u0d1a\u0d47\u0d7c\u0d24\u0d4d\u0d24\u0d41.'
                    };

                    instructionText.textContent = waterAddedMessages[currentLanguage] || waterAddedMessages['en'];

                    // Reset the instruction text after 3 seconds
                    setTimeout(() => {
                        instructionText.textContent = originalText;
                    }, 3000);
                }, 100);
            }, lastDropDelay); // Start showing water after drops have almost finished falling
        } else {
            // If the flask is not visible, show a message
            const instructionText = document.querySelector('.instruction-text');
            const originalText = instructionText.textContent;

            const flaskNeededMessages = {
                'en': 'Please place the volumetric flask first.',
                'hi': '\u0915\u0943\u092a\u092f\u093e \u092a\u0939\u0932\u0947 \u0906\u092f\u0924\u0928\u092e\u093e\u092a\u0940 \u092b\u094d\u0932\u093e\u0938\u094d\u0915 \u0930\u0916\u0947\u0902\u0964',
                'bn': '\u0985\u09a8\u09c1\u0997\u09cd\u09b0\u09b9 \u0995\u09b0\u09c7 \u09aa\u09cd\u09b0\u09a5\u09ae\u09c7 \u0986\u09af\u09bc\u09a4\u09a8\u09ae\u09be\u09aa\u0995 \u09ab\u09cd\u09b2\u09be\u09b8\u09cd\u0995 \u09b0\u09be\u0996\u09c1\u09a8\u0964',
                'gu': '\u0a95\u0ac3\u0aaa\u0abe \u0a95\u0ab0\u0ac0\u0aa8\u0ac7 \u0aaa\u0ab9\u0ac7\u0ab2\u0abe \u0ab5\u0acb\u0ab2\u0acd\u0aaf\u0ac1\u0aae\u0ac7\u0a9f\u0acd\u0ab0\u0abf\u0a95 \u0aab\u0acd\u0ab2\u0abe\u0ab8\u0acd\u0a95 \u0aae\u0ac2\u0a95\u0acb\u0964',
                'ml': '\u0d26\u0d2f\u0d35\u0d3e\u0d2f\u0d3f \u0d06\u0d26\u0d4d\u0d2f\u0d02 \u0d35\u0d4b\u0d33\u0d4d\u0d2f\u0d41\u0d2e\u0d46\u0d1f\u0d4d\u0d30\u0d3f\u0d15\u0d4d \u0d2b\u0d4d\u0d32\u0d3e\u0d38\u0d4d\u0d15\u0d4d \u0d35\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15.'
            };

            instructionText.textContent = flaskNeededMessages[currentLanguage] || flaskNeededMessages['en'];

            // Reset the instruction text after 3 seconds
            setTimeout(() => {
                instructionText.textContent = originalText;
            }, 3000);
        }
    });

    // Start button functionality
    document.querySelector('.start-btn').addEventListener('click', function() {
        // Hide the start button
        this.style.display = 'none';

        // Show the next button
        const nextButton = document.querySelector('.next-btn');
        if (nextButton) nextButton.style.display = 'block';

        // Show all elements with animation
        const elements = [
            '.volumetric-flasks',
            '.volumetric-flasks-cap',
            '.salicylic-acid',
            '.salicylic-acid-cap',
            '.distilled-water'
        ];

        // Water level in flask has been removed

        elements.forEach((selector, index) => {
            const element = document.querySelector(selector);
            setTimeout(() => {
                element.style.display = 'block';
                element.classList.add('animate-in');
            }, index * 500); // Stagger the animations
        });
    });

    // Next button functionality
    document.querySelector('.next-btn').addEventListener('click', function() {
        // Hide all apparatus elements
        const elementsToHide = [
            '.volumetric-flasks',
            '.volumetric-flasks-cap',
            '.salicylic-acid',
            '.salicylic-acid-cap',
            '.distilled-water',
            '.water-1',
            '.drop'
        ];

        // Hide each element with a fade-out effect
        elementsToHide.forEach((selector) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.classList.remove('animate-in');
                element.style.opacity = '0';
                setTimeout(() => {
                    element.style.display = 'none';
                }, 500);
            });
        });

        // Clear the background or show a completion message
        const instructionText = document.querySelector('.instruction-text');
        const completionMessages = {
            'en': 'Experiment completed successfully!',
            'hi': 'प्रयोग सफलतापूर्वक पूरा हुआ!',
            'bn': 'পরীক্ষা সফলভাবে সম্পন্ন হয়েছে!',
            'gu': 'પ્રયોગ સફળતાપૂર્વક પૂર્ણ થયો!',
            'ml': 'പരീക്ഷണം വിജയകരമായി പൂർത്തിയായി!'
        };

        instructionText.textContent = completionMessages[currentLanguage] || completionMessages['en'];

        // Hide the next button
        this.style.display = 'none';
    });

    // Language selector functionality (for changing language after initial selection)
    document.querySelector('.language-select select').addEventListener('change', function() {
        currentLanguage = this.value;
        updateLanguage();
    });

    // Function to update all text content based on selected language
    function updateLanguage() {
        const lang = translations[currentLanguage];

        // Update instruction text
        document.querySelector('.instruction-text').textContent = lang.instruction;

        // Update apparatus menu title
        document.querySelector('.apparatus-menu h2').textContent = lang.apparatus_menu;

        // Update button values
        document.getElementById('salicylic-acid').value = lang.salicylic_acid;
        document.getElementById('distilled-water').value = lang.distilled_water;
        document.getElementById('volumetric-flasks').value = lang.volumetric_flasks;

        // Update navigation buttons
        document.querySelector('.start-btn').textContent = lang.start;
        document.querySelector('.next-btn').textContent = lang.next;
    }

    // Helper function to toggle visibility
    function toggleElement(selector) {
        const element = document.querySelector(selector);
        if (element.style.display === 'none') {
            element.style.display = 'block';
            element.classList.add('animate-in');
        } else {
            element.classList.remove('animate-in');
            setTimeout(() => {
                element.style.display = 'none';
            }, 300);
        }
    }

    // No need to initialize with default language as user will select it
    // updateLanguage(); // This will be called after user selects a language

});