<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V-Lab Experiment - Working Version</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-image: url('background_img.png');
            background-size: cover;
            background-repeat: no-repeat;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        /* Navigation */
        .nav-container {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        .start-btn, .next-btn {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            margin-right: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .next-btn {
            display: none;
        }

        /* Language selector */
        .language-select {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .language-select select {
            padding: 10px;
            font-size: 16px;
            border-radius: 5px;
        }

        /* Instructions */
        .instruction-text {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            font-size: 18px;
            text-align: center;
            max-width: 80%;
            z-index: 1000;
        }

        /* Apparatus Elements */
        .salicylic-acid {
            background-image: url('salicylic_acid_1.png');
            position: absolute;
            top: 400px;
            left: 800px;
            width: 200px;
            height: 300px;
            background-size: contain;
            background-repeat: no-repeat;
            cursor: pointer;
            display: none;
            z-index: 100;
        }

        .weight-machine {
            background-image: url('weight_machine.png');
            position: absolute;
            top: 500px;
            left: 200px;
            width: 400px;
            height: 400px;
            background-size: contain;
            background-repeat: no-repeat;
            display: none;
            z-index: 100;
        }

        .weight-power-btn {
            position: absolute;
            top: 650px;
            left: 250px;
            width: 40px;
            height: 40px;
            background-color: #ff0000;
            border-radius: 50%;
            cursor: pointer;
            display: none;
            z-index: 101;
        }

        .weight-power-btn.on {
            background-color: #00ff00;
        }

        .weight-display {
            position: absolute;
            top: 620px;
            left: 320px;
            width: 80px;
            height: 30px;
            background-color: #000;
            color: #00ff00;
            font-family: monospace;
            font-size: 14px;
            text-align: center;
            line-height: 30px;
            border-radius: 5px;
            display: none;
            z-index: 101;
        }

        .distilled-water {
            background-image: url('distilled_water-01.png');
            position: absolute;
            top: 200px;
            left: 1200px;
            width: 300px;
            height: 400px;
            background-size: contain;
            background-repeat: no-repeat;
            cursor: pointer;
            display: none;
            z-index: 100;
        }

        .volumetric-flasks {
            background-image: url('new_volumetric_flask.png');
            position: absolute;
            top: 400px;
            left: 1400px;
            width: 300px;
            height: 400px;
            background-size: contain;
            background-repeat: no-repeat;
            display: none;
            z-index: 100;
        }

        /* Step 2 Elements */
        .measuring-cylinder {
            background-image: url('measuring_cylinder_hcl.png');
            position: absolute;
            top: 300px;
            left: 400px;
            width: 300px;
            height: 400px;
            background-size: contain;
            background-repeat: no-repeat;
            cursor: pointer;
            display: none;
            z-index: 100;
        }

        .beaker {
            background-image: url('beaker_1.png');
            position: absolute;
            top: 500px;
            left: 800px;
            width: 200px;
            height: 200px;
            background-size: contain;
            background-repeat: no-repeat;
            display: none;
            z-index: 100;
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .salicylic-acid.moving {
            animation: moveToScale 2s ease-in-out;
        }

        @keyframes moveToScale {
            0% { transform: translate(0, 0); }
            100% { transform: translate(-400px, 100px) scale(0.8); }
        }

        .measuring-cylinder.animating {
            animation: rotateCylinder 2s ease-in-out;
        }

        @keyframes rotateCylinder {
            0% { transform: rotate(0deg) translate(0, 0); }
            50% { transform: rotate(90deg) translate(50px, -50px); }
            100% { transform: rotate(0deg) translate(0, 0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navigation -->
        <div class="nav-container">
            <button class="start-btn">Start</button>
            <button class="next-btn">Next</button>
        </div>

        <!-- Language Selector -->
        <div class="language-select">
            <select>
                <option value="en">English</option>
                <option value="hi">हिंदी</option>
            </select>
        </div>

        <!-- Instructions -->
        <div class="instruction-text">
            Please read the instructions carefully before proceeding.
        </div>

        <!-- Apparatus Elements -->
        <div class="salicylic-acid" onclick="moveSalicylicAcid()"></div>
        <div class="weight-machine"></div>
        <div class="weight-power-btn" onclick="toggleWeightMachine()"></div>
        <div class="weight-display">00.0 mg</div>
        <div class="distilled-water" onclick="animateWater()"></div>
        <div class="volumetric-flasks"></div>
        
        <!-- Step 2 Elements -->
        <div class="measuring-cylinder" onclick="animateCylinder()"></div>
        <div class="beaker"></div>
    </div>

    <script>
        let currentStep = 1;
        let currentLanguage = 'en';
        let weightMachineOn = false;
        let salicylicAcidOnScale = false;

        const translations = {
            'en': {
                'instruction_initial': 'Please read the instructions carefully before proceeding.',
                'instruction_step1': 'Step 1: Dissolve 100 mg of salicylic acid in 100 ml of distilled water.',
                'instruction_step2': 'Step 2: Mix 8.5 ml concentrated HCl with distilled water to make 100 ml.',
                'start': 'Start',
                'next': 'Next'
            },
            'hi': {
                'instruction_initial': 'कृपया आगे बढ़ने से पहले निर्देशों को ध्यान से पढ़ें।',
                'instruction_step1': 'चरण 1: 100 मिलीग्राम सैलिसिलिक एसिड को 100 मिली आसुत जल में घोलें।',
                'instruction_step2': 'चरण 2: 8.5 मिली सांद्र HCl को आसुत जल के साथ मिलाकर 100 मिली बनाएं।',
                'start': 'प्रारंभ करें',
                'next': 'अगला'
            }
        };

        // Language change
        document.querySelector('.language-select select').addEventListener('change', function() {
            currentLanguage = this.value;
            updateLanguage();
        });

        function updateLanguage() {
            const lang = translations[currentLanguage];
            document.querySelector('.instruction-text').textContent = lang[`instruction_step${currentStep}`] || lang.instruction_initial;
            document.querySelector('.start-btn').textContent = lang.start;
            document.querySelector('.next-btn').textContent = lang.next;
        }

        // Start button
        document.querySelector('.start-btn').addEventListener('click', function() {
            this.style.display = 'none';
            document.querySelector('.next-btn').style.display = 'block';
            
            // Update instruction
            document.querySelector('.instruction-text').textContent = translations[currentLanguage].instruction_step1;
            
            // Show step 1 elements
            showElements(['.salicylic-acid', '.weight-machine', '.weight-power-btn', '.weight-display', '.distilled-water', '.volumetric-flasks']);
        });

        // Next button
        document.querySelector('.next-btn').addEventListener('click', function() {
            hideCurrentStepElements();
            currentStep++;
            
            if (currentStep === 2) {
                document.querySelector('.instruction-text').textContent = translations[currentLanguage].instruction_step2;
                showElements(['.measuring-cylinder', '.beaker', '.distilled-water']);
            } else if (currentStep > 2) {
                document.querySelector('.instruction-text').textContent = 'Experiment completed!';
                this.style.display = 'none';
            }
        });

        function showElements(selectors) {
            selectors.forEach((selector, index) => {
                setTimeout(() => {
                    const element = document.querySelector(selector);
                    if (element) {
                        element.style.display = 'block';
                        element.classList.add('fade-in');
                    }
                }, index * 200);
            });
        }

        function hideCurrentStepElements() {
            const allElements = document.querySelectorAll('.salicylic-acid, .weight-machine, .weight-power-btn, .weight-display, .distilled-water, .volumetric-flasks, .measuring-cylinder, .beaker');
            allElements.forEach(element => {
                element.style.display = 'none';
                element.classList.remove('fade-in');
            });
        }

        // Salicylic acid animation
        function moveSalicylicAcid() {
            if (!weightMachineOn) {
                alert('Please turn on the weight machine first!');
                return;
            }
            
            const acid = document.querySelector('.salicylic-acid');
            acid.classList.add('moving');
            salicylicAcidOnScale = true;
            
            setTimeout(() => {
                document.querySelector('.weight-display').textContent = '100.0 mg';
            }, 2000);
        }

        // Weight machine toggle
        function toggleWeightMachine() {
            const btn = document.querySelector('.weight-power-btn');
            const display = document.querySelector('.weight-display');
            
            weightMachineOn = !weightMachineOn;
            
            if (weightMachineOn) {
                btn.classList.add('on');
                display.style.display = 'block';
                display.textContent = salicylicAcidOnScale ? '100.0 mg' : '00.0 mg';
            } else {
                btn.classList.remove('on');
                display.style.opacity = '0.3';
            }
        }

        // Measuring cylinder animation
        function animateCylinder() {
            const cylinder = document.querySelector('.measuring-cylinder');
            cylinder.classList.add('animating');
            
            setTimeout(() => {
                cylinder.classList.remove('animating');
            }, 2000);
        }

        // Water animation (placeholder)
        function animateWater() {
            alert('Water animation - to be implemented');
        }

        // Initialize
        updateLanguage();
    </script>
</body>
</html>
