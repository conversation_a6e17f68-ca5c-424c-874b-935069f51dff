document.addEventListener('DOMContentLoaded', function() {
    // Language translations
    const translations = {
        'en': {
            'instruction_initial': 'Please read the instructions carefully before proceeding.',
            'instruction_step1': 'Step 1: Dissolve 100 mg of salicylic acid in 100 ml of distilled water.',
            'instruction_step2': 'Step 2: Mix 8.5 ml concentrated HCl with distilled water to make 100 ml.',
            'instruction_step3': 'Step 3: Prepare 5 test tubes with concentrations ranging from 0.02 mg/ml to 0.10 mg/ml using standard solution.',
            'apparatus_menu': 'Apparatus Menu',
            'salicylic_acid': 'Salicylic Acid',
            'distilled_water': 'Distilled Water',
            'volumetric_flasks': 'Volumetric Flasks',
            'weight_machine': 'Weight Machine',
            'measuring_cylinder': 'Measuring Cylinder',
            'beaker': 'Beaker',
            'colorimeter': 'Colorimeter',
            'heating_mantle': 'Heating Mantle',
            'conical_flask': 'Conical Flask',
            'start': 'Start',
            'next': 'Next'
        },
        'hi': {
            'instruction_initial': 'कृपया आगे बढ़ने से पहले निर्देशों को ध्यान से पढ़ें।',
            'instruction_step1': 'चरण 1: 100 मिलीग्राम सैलिसिलिक एसिड को 100 मिली आसुत जल में घोलें।',
            'instruction_step2': 'चरण 2: 8.5 मिली सांद्र HCl को आसुत जल के साथ मिलाकर 100 मिली बनाएं।',
            'instruction_step3': 'चरण 3: मानक समाधान का उपयोग करके 0.02 मिलीग्राम/मिली से 0.10 मिलीग्राम/मिली तक की सांद्रता वाली 5 परीक्षण नलिकाएँ तैयार करें।',
            'apparatus_menu': 'उपकरण मेनू',
            'salicylic_acid': 'सैलिसिलिक एसिड',
            'distilled_water': 'आसुत जल',
            'volumetric_flasks': 'आयतनमापी फ्लास्क',
            'weight_machine': 'वजन मशीन',
            'measuring_cylinder': 'मापक सिलेंडर',
            'beaker': 'बीकर',
            'colorimeter': 'कलरीमीटर',
            'heating_mantle': 'हीटिंग मैंटल',
            'conical_flask': 'शंक्वाकार फ्लास्क',
            'start': 'प्रारंभ करें',
            'next': 'अगला'
        },
        'bn': {
            'instruction_initial': 'অনুগ্রহ করে এগিয়ে যাওয়ার আগে নির্দেশাবলী মনোযোগ সহকারে পড়ুন।',
            'instruction_step1': 'ধাপ 1: 100 মিলিগ্রাম স্যালিসিলিক অ্যাসিড 100 মিলি পাতিত জলে দ্রবীভূত করুন।',
            'instruction_step2': 'ধাপ 2: 8.5 মিলি গাঢ় HCl পাতিত জলের সাথে মিশিয়ে 100 মিলি তৈরি করুন।',
            'instruction_step3': 'ধাপ 3: স্ট্যান্ডার্ড সলিউশন ব্যবহার করে 0.02 মিলিগ্রাম/মিলি থেকে 0.10 মিলিগ্রাম/মিলি পর্যন্ত ঘনত্বের 5টি টেস্ট টিউব প্রস্তুত করুন।',
            'apparatus_menu': 'যন্ত্রপাতি মেনু',
            'salicylic_acid': 'স্যালিসিলিক অ্যাসিড',
            'distilled_water': 'পাতিত জল',
            'volumetric_flasks': 'আয়তনমাপক ফ্লাস্ক',
            'weight_machine': 'ওজন মেশিন',
            'measuring_cylinder': 'পরিমাপক সিলিন্ডার',
            'beaker': 'বিকার',
            'colorimeter': 'কালারিমিটার',
            'heating_mantle': 'হিটিং ম্যান্টল',
            'conical_flask': 'শঙ্কু ফ্লাস্ক',
            'start': 'শুরু করুন',
            'next': 'পরবর্তী'
        },
        'gu': {
            'instruction_initial': 'કૃપા કરીને આગળ વધતા પહેલા સૂચનાઓ કાળજીપૂર્વક વાંચો.',
            'instruction_step1': 'પગલું 1: 100 મિલિગ્રામ સેલિસિલિક એસિડને 100 મિલિ ડિસ્ટિલ્ડ વોટરમાં ઓગાળો।',
            'instruction_step2': 'પગલું 2: 8.5 મિલિ સાંદ્ર HCl ને ડિસ્ટિલ્ડ વોટર સાથે મિશ્ર કરીને 100 મિલિ બનાવો।',
            'instruction_step3': 'પગલું 3: સ્ટાન્ડર્ડ સોલ્યુશનનો ઉપયોગ કરીને 0.02 મિલિગ્રામ/મિલિથી 0.10 મિલિગ્રામ/મિલિ સુધીની સાંદ્રતા ધરાવતી 5 ટેસ્ટ ટ્યુબ તૈયાર કરો.',
            'apparatus_menu': 'ઉપકરણ મેનુ',
            'salicylic_acid': 'સેલિસિલિક એસિડ',
            'distilled_water': 'ડિસ્ટિલ્ડ વોટર',
            'volumetric_flasks': 'વોલ્યુમેટ્રિક ફ્લાસ્ક',
            'weight_machine': 'વજન મશીન',
            'measuring_cylinder': 'માપક સિલિન્ડર',
            'beaker': 'બીકર',
            'colorimeter': 'કલરીમીટર',
            'heating_mantle': 'હીટિંગ મેન્ટલ',
            'conical_flask': 'શંકુ આકારનું ફ્લાસ્ક',
            'start': 'શરૂ કરો',
            'next': 'આગળ'
        },
        'ml': {
            'instruction_initial': 'മുന്നോട്ട് പോകുന്നതിന് മുമ്പ് നിർദ്ദേശങ്ങൾ ശ്രദ്ധാപൂർവ്വം വായിക്കുക.',
            'instruction_step1': 'ഘട്ടം 1: 100 മില്ലിഗ്രാം സാലിസിലിക് ആസിഡ് 100 മില്ലി ഡിസ്റ്റിൽഡ് വാട്ടറിൽ ലയിപ്പിക്കുക।',
            'instruction_step2': 'ഘട്ടം 2: 8.5 മില്ലി കേന്ദ്രീകൃത HCl ഡിസ്റ്റിൽഡ് വാട്ടറുമായി മിശ്രിച്ച് 100 മില്ലി ഉണ്ടാക്കുക।',
            'instruction_step3': 'ഘട്ടം 3: സ്റ്റാൻഡേർഡ് സൊലൂഷൻ ഉപയോഗിച്ച് 0.02 മില്ലിഗ്രാം/മില്ലി മുതൽ 0.10 മില്ലിഗ്രാം/മില്ലി വരെയുള്ള സാന്ദ്രതയുള്ള 5 ടെസ്റ്റ് ട്യൂബുകൾ തയ്യാറാക്കുക.',
            'apparatus_menu': 'ഉപകരണ മെനു',
            'salicylic_acid': 'സാലിസിലിക് ആസിഡ്',
            'distilled_water': 'ഡിസ്റ്റിൽഡ് വാട്ടർ',
            'volumetric_flasks': 'വോള്യുമെട്രിക് ഫ്ലാസ്ക്',
            'weight_machine': 'തൂക്ക യന്ത്രം',
            'measuring_cylinder': 'അളവ് സിലിണ്ടർ',
            'beaker': 'ബീക്കർ',
            'colorimeter': 'കളറിമീറ്റർ',
            'heating_mantle': 'ഹീറ്റിംഗ് മാന്റിൽ',
            'conical_flask': 'കോണിക്കൽ ഫ്ലാസ്ക്',
            'start': 'ആരംഭിക്കുക',
            'next': 'അടുത്തത്'
        }
    };

    // Set default language to English
    let currentLanguage = 'en';

    // Update all text content based on selected language
    function updateLanguage() {
        const lang = translations[currentLanguage];

        // Update instruction text
        document.querySelector('.instruction-text').textContent = lang.instruction_initial;

        // Update apparatus menu title
        document.querySelector('.apparatus-menu h2').textContent = lang.apparatus_menu;

        // Update button values
        document.getElementById('salicylic-acid').value = lang.salicylic_acid;
        document.getElementById('distilled-water').value = lang.distilled_water;
        document.getElementById('volumetric-flasks').value = lang.volumetric_flasks;
        document.getElementById('weight-machine').value = lang.weight_machine;
        document.getElementById('measuring-cylinder').value = lang.measuring_cylinder;
        document.getElementById('beaker').value = lang.beaker;

        // Update navigation buttons
        document.querySelector('.start-btn').textContent = lang.start;
        document.querySelector('.next-btn').textContent = lang.next;
    }

    // Add event listener to language dropdown
    document.querySelector('.language-select select').addEventListener('change', function() {
        currentLanguage = this.value;
        updateLanguage();
    });

    // Initialize with default language (English)
    updateLanguage();

    // Variable to track if salicylic acid is on the scale
    let salicylicAcidOnScale = false;

    // Initially hide all apparatus elements
    const apparatusElements = [
        '.volumetric-flasks',
        '.volumetric-flasks-cap',
        '.spatula',
        '.salicylic-acid',
        '.distilled-water',
        '.water-1',
        '.drop',
        '.measuring-cylinder',
        '.beaker',
        '.weight-machine',
        '.weight-display',
        '.weight-power-btn',
        '.standard-solution',
        '.test-tube-1',
        '.test-tube-2',
        '.test-tube-3',
        '.test-tube-4',
        '.test-tube-5',
        '.test-tube-rack'
    ];

    apparatusElements.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = 'none';
        });
    });

    // Hide the next button initially
    document.querySelector('.next-btn').style.display = 'none';

    // Disable apparatus buttons initially
    const apparatusButtons = [
        'volumetric-flasks',
        'salicylic-acid',
        'distilled-water',
        'weight-machine',
        'measuring-cylinder',
        'beaker'
    ];

    // Add a disabled class to all apparatus buttons
    apparatusButtons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.classList.add('disabled');
            button.style.opacity = '0.5';
            button.style.cursor = 'not-allowed';
        }
    });

    // Start button functionality
    document.querySelector('.start-btn').addEventListener('click', function() {
        // Hide the start button
        this.style.display = 'none';

        // Show the next button
        document.querySelector('.next-btn').style.display = 'block';

        // Update instruction text to show step 1
        const instructionText = document.querySelector('.instruction-text');
        instructionText.textContent = translations[currentLanguage].instruction_step1 || translations['en'].instruction_step1;

        // Define the objects to show with animation
        const objectsToShow = [
            {
                selector: '.volumetric-flasks',
                delay: 100
            },
            {
                selector: '.volumetric-flasks-cap',
                delay: 500
            },
            {
                selector: '.salicylic-acid',
                delay: 1000
            },
            {
                selector: '.weight-machine',
                delay: 1500
            },
            {
                selector: '.weight-power-btn',
                delay: 2000
            },
            {
                selector: '.distilled-water',
                delay: 2500
            }
        ];

        // Add a variable to track if we've added the click event to the acid image
        let acidImageClickAdded = false;

        // Show each object with animation after its delay
        objectsToShow.forEach(obj => {
            setTimeout(() => {
                const element = document.querySelector(obj.selector);
                if (element) {
                    element.style.display = 'block';
                    element.style.opacity = '0';

                    // Add click event to the salicylic acid image
                    if (obj.selector === '.salicylic-acid' && !acidImageClickAdded) {
                        element.style.cursor = 'pointer'; // Change cursor to indicate it's clickable
                        element.addEventListener('click', handleSalicylicAcidClick);
                        acidImageClickAdded = true;
                    }

                    // Fade in animation
                    setTimeout(() => {
                        element.style.transition = 'opacity 0.5s ease-in-out';
                        element.style.opacity = '1';
                    }, 50);
                }
            }, obj.delay);
        });

        // Enable apparatus buttons after all objects are shown
        setTimeout(() => {
            // Only enable step 1 buttons
            const step1Buttons = ['volumetric-flasks', 'salicylic-acid', 'distilled-water', 'weight-machine'];

            apparatusButtons.forEach(buttonId => {
                // Only enable buttons for step 1
                if (step1Buttons.includes(buttonId)) {
                    const button = document.getElementById(buttonId);
                    if (button) {
                        button.classList.remove('disabled');
                        button.style.opacity = '1';
                        button.style.cursor = 'pointer';

                        // Add event listeners based on button ID
                        if (buttonId === 'volumetric-flasks') {
                            button.addEventListener('click', function() {
                                // Don't toggle, just ensure they're visible
                                const flaskElement = document.querySelector('.volumetric-flasks');
                                const capElement = document.querySelector('.volumetric-flasks-cap');

                                if (flaskElement) flaskElement.style.display = 'block';
                                if (capElement) capElement.style.display = 'block';
                            });
                        } else if (buttonId === 'salicylic-acid') {
                            button.addEventListener('click', function() {
                                // Don't toggle, just ensure it's visible
                                const acidElement = document.querySelector('.salicylic-acid');
                                if (acidElement) {
                                    acidElement.style.display = 'block';

                                    // Show a message to click directly on the acid image
                                    const instructionText = document.querySelector('.instruction-text');
                                    const originalText = instructionText.textContent;

                                    const clickAcidMessages = {
                                        'en': 'Click directly on the salicylic acid image to place it on the scale.',
                                        'hi': 'सैलिसिलिक एसिड को तराजू पर रखने के लिए सीधे इसकी छवि पर क्लिक करें।',
                                        'bn': 'স্কেলে রাখতে সরাসরি স্যালিসিলিক অ্যাসিড ইমেজে ক্লিক করুন।',
                                        'gu': 'સ્કેલ પર મૂકવા માટે સીધા સેલિસિલિક એસિડ ઇમેજ પર ક્લિક કરો.',
                                        'ml': 'സ്കെയിലിൽ വയ്ക്കാൻ നേരിട്ട് സാലിസിലിക് ആസിഡ് ചിത്രത്തിൽ ക്ലിക്ക് ചെയ്യുക.'
                                    };

                                    instructionText.textContent = clickAcidMessages[currentLanguage] || clickAcidMessages['en'];

                                    // Reset the instruction text after 4 seconds
                                    setTimeout(() => {
                                        instructionText.textContent = originalText;
                                    }, 4000);
                                }
                            });
                        } else if (buttonId === 'distilled-water') {
                            button.addEventListener('click', function() {
                                // Don't toggle, just ensure it's visible
                                const waterElement = document.querySelector('.distilled-water');
                                if (waterElement) waterElement.style.display = 'block';

                                // Handle the distilled water click (drop animation)
                                handleDistilledWaterClick();
                            });
                        } else if (buttonId === 'weight-machine') {
                            button.addEventListener('click', function() {
                                // Don't toggle, just ensure they're visible
                                const machineElement = document.querySelector('.weight-machine');
                                const powerBtnElement = document.querySelector('.weight-power-btn');

                                if (machineElement) machineElement.style.display = 'block';
                                if (powerBtnElement) powerBtnElement.style.display = 'block';
                            });
                        }
                    }
                }
            });


            // Add click event to the distilled water bottle for the drop animation
            const distilledWaterElement = document.querySelector('.distilled-water');
            if (distilledWaterElement) {
                distilledWaterElement.addEventListener('click', handleDistilledWaterClick);
            }

            // The salicylicAcidOnScale variable is now defined globally

            // Add click event to the weight machine power button
            const weightPowerBtn = document.querySelector('.weight-power-btn');
            if (weightPowerBtn) {
                weightPowerBtn.addEventListener('click', function() {
                    // Toggle the power button state
                    this.classList.toggle('on');

                    // Get the weight display
                    const weightDisplay = document.querySelector('.weight-display');

                    if (this.classList.contains('on')) {
                        // Turn on the weight machine - display is initially blank
                        weightDisplay.style.display = 'block';

                        // Fade in animation
                        setTimeout(() => {
                            weightDisplay.style.opacity = '1';

                            // Display is blank initially or shows 100 mg if salicylic acid is on the scale
                            if (salicylicAcidOnScale) {
                                weightDisplay.textContent = '100 mg';
                            } else {
                                weightDisplay.textContent = '0 mg';
                            }

                            // Check if salicylic acid element has the on-scale class
                            const salicylicAcidElement = document.querySelector('.salicylic-acid');
                            if (salicylicAcidElement && salicylicAcidElement.classList.contains('on-scale')) {
                                salicylicAcidOnScale = true;
                                weightDisplay.textContent = '100 mg';
                            }

                            // Show a message that the weight machine is on
                            const instructionText = document.querySelector('.instruction-text');
                            const originalText = instructionText.textContent;

                            const weightOnMessages = {
                                'en': 'Weight machine is now on. Next, click on the salicylic acid to place it on the scale.',
                                'hi': 'वजन मशीन अब चालू है। अब, सैलिसिलिक एसिड पर क्लिक करके इसे तराजू पर रखें।',
                                'bn': 'ওজন মেশিন এখন চালু আছে। এবার, স্যালিসিলিক অ্যাসিডে ক্লিক করে এটি স্কেলে রাখুন।',
                                'gu': 'વજન મશીન હવે ચાલુ છે. હવે, સેલિસિલિક એસિડ પર ક્લિક કરીને તેને સ્કેલ પર મૂકો.',
                                'ml': 'വെയ്റ്റ് മെഷീൻ ഇപ്പോൾ ഓണാണ്. അടുത്തത്, സാലിസിലിക് ആസിഡിൽ ക്ലിക്ക് ചെയ്ത് അത് സ്കെയിലിൽ വയ്ക്കുക.'
                            };

                            instructionText.textContent = weightOnMessages[currentLanguage] || weightOnMessages['en'];

                            // Reset the instruction text after 5 seconds (increased from 3 seconds)
                            setTimeout(() => {
                                instructionText.textContent = originalText;
                            }, 5000);
                        }, 50);
                    } else {
                        // Turn off the weight machine - display stays visible but dims
                        weightDisplay.style.opacity = '0.3';

                        // Show a message that the weight machine is off
                        const instructionText = document.querySelector('.instruction-text');
                        const originalText = instructionText.textContent;

                        const weightOffMessages = {
                            'en': 'Weight machine is now off.',
                            'hi': 'वजन मशीन अब बंद है।',
                            'bn': 'ওজন মেশিন এখন বন্ধ আছে।',
                            'gu': 'વજન મશીન હવે બંધ છે.',
                            'ml': 'വെയ്റ്റ് മെഷീൻ ഇപ്പോൾ ഓഫാണ്.'
                        };

                        instructionText.textContent = weightOffMessages[currentLanguage] || weightOffMessages['en'];

                        // Reset the instruction text after 3 seconds
                        setTimeout(() => {
                            instructionText.textContent = originalText;
                        }, 3000);
                    }
                });
            }
        }, 2000); // Enable buttons after all objects are shown
    });

    // Variable to track current step
    let currentStep = 1;

    // Next button functionality
    document.querySelector('.next-btn').addEventListener('click', function() {
        // Reset salicylic acid position and state
        const salicylicAcidElement = document.querySelector('.salicylic-acid');
        if (salicylicAcidElement) {
            salicylicAcidElement.classList.remove('on-scale');
            salicylicAcidElement.style.animation = 'none';
            salicylicAcidElement.style.left = '50%';
            salicylicAcidElement.style.top = '60%';
            salicylicAcidElement.style.transform = 'translate(-50%, -50%)';

            // Reset the salicylicAcidOnScale variable
            salicylicAcidOnScale = false;
        }

        // Hide all apparatus elements
        apparatusElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.classList.remove('animate-in');
                element.style.opacity = '0';
                setTimeout(() => {
                    element.style.display = 'none';
                }, 500);
            });
        });

        // Get the instruction text element
        const instructionText = document.querySelector('.instruction-text');

        // Check which step we're on
        if (currentStep === 1) {
            // Move to step 2
            currentStep = 2;

            // Update instruction text to show step 2
            instructionText.textContent = translations[currentLanguage].instruction_step2 || translations['en'].instruction_step2;

            // Show step 2 objects with animation
            const step2Objects = [
                {
                    selector: '.measuring-cylinder',
                    delay: 10
                },
                {
                    selector: '.beaker',
                    delay: 500
                },
                {
                    selector: '.distilled-water',
                    delay: 1000
                }
            ];

            // Show each object with animation after its delay
            step2Objects.forEach(obj => {
                setTimeout(() => {
                    const element = document.querySelector(obj.selector);
                    if (element) {
                        element.style.display = 'block';
                        element.style.opacity = '0';

                        // Fade in animation
                        setTimeout(() => {
                            element.style.transition = 'opacity 0.5s ease-in-out';
                            element.style.opacity = '1';
                        }, 50);
                    }
                }, obj.delay);
            });

            // Enable apparatus buttons for step 2
            setTimeout(() => {
                const step2Buttons = ['measuring-cylinder', 'beaker', 'distilled-water'];

                step2Buttons.forEach(buttonId => {
                    const button = document.getElementById(buttonId);
                    if (button) {
                        button.classList.remove('disabled');
                        button.style.opacity = '1';
                        button.style.cursor = 'pointer';

                        // Add event listeners based on button ID
                        if (buttonId === 'measuring-cylinder') {
                            button.addEventListener('click', function() {
                                toggleElement('.measuring-cylinder');
                            });
                        } else if (buttonId === 'beaker') {
                            button.addEventListener('click', function() {
                                toggleElement('.beaker');
                            });
                        } else if (buttonId === 'distilled-water') {
                            button.addEventListener('click', function() {
                                toggleElement('.distilled-water');
                            });
                        }
                    }
                });
            }, 1500);

            // Keep the next button visible for step 2
            this.textContent = translations[currentLanguage].next || translations['en'].next;
        } else if (currentStep === 2) {
            // Move to step 3
            currentStep = 3;

            // Update instruction text to show step 3
            instructionText.textContent = translations[currentLanguage].instruction_step3 || translations['en'].instruction_step3;

            // Show step 3 objects with animation - test tubes
            const step3Objects = [
                {
                    selector: '.test-tube-rack', // Add the test tube rack first
                    delay: 100
                },
                {
                    selector: '.standard-solution',
                    delay: 300
                },
                {
                    selector: '.test-tube-1',
                    delay: 500
                },
                {
                    selector: '.test-tube-2',
                    delay: 700
                },
                {
                    selector: '.test-tube-3',
                    delay: 900
                },
                {
                    selector: '.test-tube-4',
                    delay: 
                    1100
                },
                {
                    selector: '.test-tube-5',
                    delay: 1300
                }
            ];

            // Show each object with animation after its delay
            step3Objects.forEach(obj => {
                setTimeout(() => {
                    const element = document.querySelector(obj.selector);
                    if (element) {
                        element.style.display = 'block';
                        element.style.opacity = '0';

                        // Fade in animation
                        setTimeout(() => {
                            element.style.transition = 'opacity 0.5s ease-in-out';
                            element.style.opacity = '1';
                        }, 50);
                    }
                }, obj.delay);
            });

            // Keep the next button visible for step 3
            this.textContent = translations[currentLanguage].next || translations['en'].next;
        } else {
            // We're at the end of the experiment
            // Show completion message
            const completionMessages = {
                'en': 'Experiment completed successfully!',
                'hi': 'प्रयोग सफलतापूर्वक पूरा हुआ!',
                'bn': 'পরীক্ষা সফলভাবে সম্পন্ন হয়েছে!',
                'gu': 'પ્રયોગ સફળતાપૂર્વક પૂર્ણ થયો!',
                'ml': 'പരീക്ഷണം വിജയകരമായി പൂർത്തിയായി!'
            };

            instructionText.textContent = completionMessages[currentLanguage] || completionMessages['en'];

            // Hide the next button
            this.style.display = 'none';
        }
    });

    // Helper function to toggle visibility
    function toggleElement(selector) {
        const element = document.querySelector(selector);
        if (element.style.display === 'none') {
            element.style.display = 'block';
            element.classList.add('animate-in');
        } else {
            element.classList.remove('animate-in');
            setTimeout(() => {
                element.style.display = 'none';
            }, 300);
        }
    }

    // Function to handle distilled water bottle click
    function handleDistilledWaterClick() {
        // Only proceed if the flask is visible
        const flaskElement = document.querySelector('.volumetric-flasks');
        if (flaskElement.style.display === 'block') {
            // Get the water element and drops
            const waterElement = document.querySelector('.water-1');
            const drops = document.querySelectorAll('.drop');

            // Start the animation sequence
            // 1. Show and animate the drops falling
            drops.forEach((drop, index) => {
                setTimeout(() => {
                    // Make sure the drop is visible
                    drop.style.display = 'block';
                    drop.style.opacity = '1';

                    // Add the falling animation class
                    drop.classList.add('falling');

                    // Remove the animation class after it completes
                    setTimeout(() => {
                        drop.classList.remove('falling');
                        drop.style.opacity = '0';
                        drop.style.display = 'none';
                    }, 1500); // Match the animation duration
                }, index * 300); // Stagger the drops
            });

            // 2. After drops have finished falling, show the water in the flask
            // Wait for the last drop to complete its animation
            const lastDropDelay = (drops.length - 1) * 300 + 1000;

            setTimeout(() => {
                // Show the water in the flask with a fade-in effect
                waterElement.style.display = 'block';
                setTimeout(() => {
                    waterElement.style.opacity = '1';

                    // 3. Show a message that water has been added
                    const instructionText = document.querySelector('.instruction-text');
                    const originalText = instructionText.textContent;

                    // Create water added message in the current language
                    const waterAddedMessages = {
                        'en': 'Distilled water has been added to the flask.',
                        'hi': 'आसुत जल फ्लास्क में डाला गया है।',
                        'bn': 'পাতিত জল ফ্লাস্কে যোগ করা হয়েছে।',
                        'gu': 'ડિસ્ટિલ્ડ વોટર ફ્લાસ્કમાં ઉમેરવામાં આવ્યું છે.',
                        'ml': 'ഡിസ്റ്റിൽഡ് വാട്ടർ ഫ്ലാസ്കിലേക്ക് ചേർത്തു.'
                    };

                    instructionText.textContent = waterAddedMessages[currentLanguage] || waterAddedMessages['en'];

                    // Reset the instruction text after 3 seconds
                    setTimeout(() => {
                        instructionText.textContent = originalText;
                    }, 3000);
                }, 100);
            }, lastDropDelay);
        } else {
            // If the flask is not visible, show a message
            const instructionText = document.querySelector('.instruction-text');
            const originalText = instructionText.textContent;

            const flaskNeededMessages = {
                'en': 'Please place the volumetric flask first.',
                'hi': 'कृपया पहले आयतनमापी फ्लास्क रखें।',
                'bn': 'অনুগ্রহ করে প্রথমে আয়তনমাপক ফ্লাস্ক রাখুন।',
                'gu': 'કૃપા કરીને પહેલા વોલ્યુમેટ્રિક ફ્લાસ્ક મૂકો.',
                'ml': 'ദയവായി ആദ്യം വോള്യുമെട്രിക് ഫ്ലാസ്ക് വയ്ക്കുക.'
            };

            instructionText.textContent = flaskNeededMessages[currentLanguage] || flaskNeededMessages['en'];

            // Reset the instruction text after 3 seconds
            setTimeout(() => {
                instructionText.textContent = originalText;
            }, 3000);
        }
    }

    // Function to handle salicylic acid click
    function handleSalicylicAcidClick() {
        // Only proceed if the weight machine is visible
        const weightMachine = document.querySelector('.weight-machine');
        const weightPowerBtn = document.querySelector('.weight-power-btn');
        const weightDisplay = document.querySelector('.weight-display');
        const salicylicAcidElement = document.querySelector('.salicylic-acid');

        // Check if the weight machine is visible and the acid is not already on the scale
        // We don't check if the acid is visible because we're clicking directly on it
        if (weightMachine.style.display === 'block' && !salicylicAcidOnScale) {

            // Using percentage-based positioning for smooth animation
            // This ensures the animation works well on different screen sizes

            // Target position (center of the weight machine)
            const endLeft = 28; // Target horizontal position (percentage)
            const endBottom = 15; // Target vertical position (percentage)

            // Show a message that the animation is starting
            const instructionText = document.querySelector('.instruction-text');
            const originalText = instructionText.textContent;

            const startMessages = {
                'en': 'Moving salicylic acid to the weight machine...',
                'hi': 'सैलिसिलिक एसिड को वजन मशीन पर ले जा रहे हैं...',
                'bn': 'স্যালিসিলিক অ্যাসিড ওজন মেশিনে স্থানান্তর করা হচ্ছে...',
                'gu': 'સેલિસિલિક એસિડને વજન મશીન પર લઈ જઈ રહ્યા છીએ...',
                'ml': 'സാലിസിലിക് ആസിഡ് വെയ്റ്റ് മെഷീനിലേക്ക് നീക്കുന്നു...'
            };

            instructionText.textContent = startMessages[currentLanguage] || startMessages['en'];

            // Create a timeline for more complex animation with better control
            const tl = gsap.timeline({
                onComplete: function() {
                    // This function runs when the animation is complete
                    salicylicAcidElement.classList.add('on-scale');
                    salicylicAcidOnScale = true;

                    // If the weight machine is on, update the display
                    if (weightPowerBtn.classList.contains('on') &&
                        weightDisplay.style.display === 'block') {
                        // Set the weight to 100 mg
                        weightDisplay.textContent = '100 mg';
                    }

                    // Show a message that salicylic acid has been placed on the scale
                    const salicylicAcidMessages = {
                        'en': 'Salicylic acid has been placed on the scale. Weight: 100 mg. Step 1 of the experiment is now complete.',
                        'hi': 'सैलिसिलिक एसिड को तराजू पर रखा गया है। वजन: 100 मिलीग्राम। प्रयोग का चरण 1 अब पूरा हो गया है।',
                        'bn': 'স্যালিসিলিক অ্যাসিড স্কেলে রাখা হয়েছে। ওজন: 100 মিলিগ্রাম। পরীক্ষার ধাপ 1 এখন সম্পূর্ণ হয়েছে।',
                        'gu': 'સેલિસિલિક એસિડ સ્કેલ પર મૂકવામાં આવ્યું છે. વજન: 100 મિલિગ્રામ. પ્રયોગનું પગલું 1 હવે પૂર્ણ થયું છે.',
                        'ml': 'സാലിസിലിക് ആസിഡ് സ്കെയിലിൽ വച്ചിരിക്കുന്നു. ഭാരം: 100 മില്ലിഗ്രാം. പരീക്ഷണത്തിന്റെ ഘട്ടം 1 ഇപ്പോൾ പൂർത്തിയായി.'
                    };

                    instructionText.textContent = salicylicAcidMessages[currentLanguage] || salicylicAcidMessages['en'];

                    // Reset the instruction text after 6 seconds (increased for better readability)
                    setTimeout(() => {
                        instructionText.textContent = originalText;
                    }, 6000);
                }
            });

            // Add animations to the timeline with enhanced motion
            tl.to(salicylicAcidElement, {
                duration: 0.6,  // Duration in seconds
                y: "-=3%",      // Initial lift - move up by 3% of viewport height
                scale: 0.9,     // Slightly reduce size during lift
                ease: "power2.out",  // Smooth acceleration for lift
                transformOrigin: "center bottom" // Pivot from bottom center
            })
            .to(salicylicAcidElement, {
                duration: 1.2,   // Longer duration for main movement
                left: endLeft + "%",  // Move to target position
                bottom: endBottom + "%", // Move to target position
                scale: 0.8,      // Scale down to 80%
                ease: "power3.inOut", // More pronounced easing for main movement
                transformOrigin: "center bottom" // Maintain pivot point
            })
            .to(salicylicAcidElement, {
                duration: 0.4,   // Duration for settling
                y: "+=1%",       // Settle down slightly
                scale: 0.75,     // Final scale
                ease: "elastic.out(1, 0.3)", // More natural bouncy effect
                transformOrigin: "center bottom" // Maintain pivot point
            });

            // Add a subtle rotation during the movement for more realism
            tl.to(salicylicAcidElement, {
                rotation: -5,    // Slight counter-clockwise rotation
                duration: 0.4,
                ease: "power1.inOut"
            }, 0.3) // Start 0.3 seconds into the animation
            .to(salicylicAcidElement, {
                rotation: 3,     // Slight clockwise rotation
                duration: 0.5,
                ease: "power1.inOut"
            }, 0.7) // Start 0.7 seconds into the animation
            .to(salicylicAcidElement, {
                rotation: 0,     // Return to normal rotation
                duration: 0.4,
                ease: "power1.out"
            }, 1.2); // Start 1.2 seconds into the animation
        }
    }
});
