
/* Different colors for each button */
/* .apparatus-menu input#salicylic-acid {
 background: linear-gradient(145deg, #FF5722, #D84315);
}

.apparatus-menu input#salicylic-acid:hover {
 background: linear-gradient(145deg, #F4511E, #BF360C);
}

.apparatus-menu input#distilled-water {
 background: linear-gradient(145deg, #00BCD4, #006064);
}

.apparatus-menu input#distilled-water:hover {
 background: linear-gradient(145deg, #00ACC1, #00838F);
}

.apparatus-menu input#volumetric-flasks {
 background: linear-gradient(145deg, #9C27B0, #4A148C);
}

.apparatus-menu input#volumetric-flasks:hover {
 background: linear-gradient(145deg, #8E24AA, #4A148C);
}

.apparatus-menu input#weight-machine {
 background: linear-gradient(145deg, #4CAF50, #2E7D32);
}

.apparatus-menu input#weight-machine:hover {
 background: linear-gradient(145deg, #43A047, #1B5E20);
}

.apparatus-menu input#measuring-cylinder {
 background: linear-gradient(145deg, #FFC107, #FF8F00);
}

.apparatus-menu input#measuring-cylinder:hover {
 background: linear-gradient(145deg, #FFB300, #FF6F00);
}

.apparatus-menu input#beaker {
 background: linear-gradient(145deg, #E91E63, #880E4F);
}

.apparatus-menu input#beaker:hover {
 background: linear-gradient(145deg, #D81B60, #AD1457);
} */





/* Animation classes */
.animate-in {
  animation: fadeInAndMove 1s ease-out forwards;
}

@keyframes fadeInAndMove {
  0% {
      opacity: 0;
      transform: translateY(50px);
  }
  100% {
      opacity: 1;
      transform: translateY(0);
  }
}




/* Animation for salicylic acid movement - Not needed with GSAP */
/* We'll use GSAP for animations instead of CSS keyframes */
/* .spatula{
  background-image: url(spatulla.png);
  position: absolute;
  left: 2000px;
  top: 2100px;
  width: 400px;
  height: 400px;
  z-index: 1000;
  background-repeat: no-repeat;
  background-size: contain;
} */


/* Weight machine on-off button */

.measuring-cylinder{
  background-image: url(measuring_cylinder_hcl.png);
  position: absolute;
  left: 1340px;
  top: 1700px;
  width: 1200px;
  height: 800px;
  z-index: 1000;
  background-repeat: no-repeat;
  background-size: contain;
}
.beaker{
  background-image: url(beaker_1.png);
  position: absolute;
  left: 2200px;
  top: 1900px;
  width: 900px;
  height: 600px;
  z-index: 1000;
  background-repeat: no-repeat;
  background-size: contain;
}
.water-1{
  background-image: url(water_1-01.png);
  position: absolute;
  left: 2250px;
  top: 1440px;
  width: 870px;
  height: 1530px;
  z-index: 999;
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
  transition: opacity 1s ease;
  display: none;
}




.drop{
  background-image: url(drop.png);
  position: absolute;
  width: 30px;
  height: 50px;
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
  display: none;
  z-index: 1001;
  pointer-events: none;
}
/* Position the drops to align with the flask opening */
.drop:nth-of-type(1) {
  left: 2675px;
  top: 1500px;
}

.drop:nth-of-type(2) {
  left: 2685px;
  top: 1500px;
}

.drop:nth-of-type(3) {
  left: 2665px;
  top: 1500px;
}

@keyframes dropFall {
  0% {
      transform: translateY(0);
      opacity: 0;
  }
  10% {
      opacity: 1;
  }
  80% {
      opacity: 1;
  }
  100% {
      transform: translateY(300px);
      opacity: 0;
  }
}

.drop.falling {
  animation: dropFall 1.5s linear forwards;
  display: block !important;
  opacity: 1;
}

/* Step 3 elements */
.standard-solution {
  background-image: url(standard_solution.png);
  position: absolute;
  left: 10%; /* Using percentage for horizontal positioning */
  bottom: 20%; /* Using percentage for vertical positioning */
  width: 10%; /* Using percentage for width */
  height: 25%; /* Using percentage for height */
  z-index: 1000;
  background-repeat: no-repeat;
  background-size: contain;
}

.test-tube-rack {
  background-image: url(test_tube_rack.png);
  position: absolute;
  left: 40%; /* Using percentage for horizontal positioning */
  bottom: 10%; /* Using percentage for vertical positioning */
  width: 40%; /* Using percentage for width */
  height: 15%; /* Using percentage for height */
  z-index: 1000;
  background-repeat: no-repeat;
  background-size: contain;
}

.test-tube-1 {
  background-image: url(test_tube_1.png);
  position: absolute;
  left: 20%; /* Using percentage for horizontal positioning */
  bottom: 20%; /* Using percentage for vertical positioning */
  width: 5%; /* Using percentage for width */
  height: 20%; /* Using percentage for height */
  z-index: 1001;
  background-repeat: no-repeat;
  background-size: contain;
}

.test-tube-2 {
  background-image: url(test_tube_2.png);
  position: absolute;
  left: 30%; /* Using percentage for horizontal positioning */
  bottom: 20%; /* Using percentage for vertical positioning */
  width: 5%; /* Using percentage for width */
  height: 20%; /* Using percentage for height */
  z-index: 1001;
  background-repeat: no-repeat;
  background-size: contain;
}

.test-tube-3 {
  background-image: url(test_tube_3.png);
  position: absolute;
  left: 40%; /* Using percentage for horizontal positioning */
  bottom: 20%; /* Using percentage for vertical positioning */
  width: 5%; /* Using percentage for width */
  height: 20%; /* Using percentage for height */
  z-index: 1001;
  background-repeat: no-repeat;
  background-size: contain;
}

.test-tube-4 {
  background-image: url(test_tube_4.png);
  position: absolute;
  left: 50%; /* Using percentage for horizontal positioning */
  bottom: 20%; /* Using percentage for vertical positioning */
  width: 5%; /* Using percentage for width */
  height: 20%; /* Using percentage for height */
  z-index: 1001;
  background-repeat: no-repeat;
  background-size: contain;
}

.test-tube-5 {
  background-image: url(test_tube_5.png);
  position: absolute;
  left: 60%; /* Using percentage for horizontal positioning */
  bottom: 20%; /* Using percentage for vertical positioning */
  width: 5%; /* Using percentage for width */
  height: 20%; /* Using percentage for height */
  z-index: 1001;
  background-repeat: no-repeat;
  background-size: contain;
}












